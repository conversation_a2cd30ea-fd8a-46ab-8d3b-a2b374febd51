#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Professional Search Component for Age<PERSON>lami PM
Enhanced with modern UX patterns and performance optimizations
"""

import flet as ft
from typing import Callable, Optional, List, Dict, Any
from datetime import datetime
import threading
import time
import logging

logger = logging.getLogger(__name__)

class ProfessionalSearchEngine:
    """High-performance search engine with caching and indexing"""

    def __init__(self, app_instance):
        self.app = app_instance
        self.search_cache = {}
        self.cache_timeout = 30  # seconds
        self.last_cache_update = {}

    def search(self, query: str, max_results: int = 12) -> List[Dict[str, Any]]:
        """Perform optimized search with caching"""
        if not query or len(query.strip()) < 2:
            return []

        query = query.strip().lower()
        cache_key = f"{query}_{max_results}"

        # Check cache first
        if cache_key in self.search_cache:
            cache_time = self.last_cache_update.get(cache_key, 0)
            if time.time() - cache_time < self.cache_timeout:
                return self.search_cache[cache_key]

        # Perform search
        results = self._perform_search(query, max_results)

        # Cache results
        self.search_cache[cache_key] = results
        self.last_cache_update[cache_key] = time.time()

        return results

    def _perform_search(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """Execute the actual search logic"""
        try:
            results = []

            # Search clients with enhanced matching
            clients = self.app.db.get_all_clients()
            for client in clients[:50]:  # Limit for performance
                score = self._calculate_relevance_score(query, [
                    client.name,
                    client.business_name or "",
                    client.email or "",
                    client.vat_number or "",
                    client.tax_code or ""
                ])

                if score > 0:
                    results.append({
                        'type': 'client',
                        'id': str(client.id),
                        'title': client.name,
                        'subtitle': client.business_name or client.email or "Cliente",
                        'icon': ft.Icons.PERSON_OUTLINE,
                        'score': score,
                        'data': client,
                        'category': 'Clienti'
                    })

            # Search projects
            projects = self.app.db.get_all_projects()
            for project in projects[:50]:  # Limit for performance
                client_name = ""
                if project.client_id:
                    try:
                        client = self.app.db.get_client(project.client_id)
                        if client:
                            client_name = client.name
                    except:
                        pass

                score = self._calculate_relevance_score(query, [
                    project.name,
                    project.reference_code or "",
                    project.description or "",
                    client_name
                ])

                if score > 0:
                    results.append({
                        'type': 'project',
                        'id': str(project.id),
                        'title': project.name,
                        'subtitle': f"{project.reference_code or 'N/A'} • {client_name}",
                        'icon': ft.Icons.FOLDER_OUTLINED,
                        'score': score,
                        'data': project,
                        'category': 'Progetti'
                    })

            # Search deadlines
            deadlines = self.app.db.get_all_deadlines()
            for deadline in deadlines[:50]:  # Limit for performance
                project_name = ""
                if deadline.project_id:
                    try:
                        project = self.app.db.get_project(deadline.project_id)
                        if project:
                            project_name = project.name
                    except:
                        pass

                score = self._calculate_relevance_score(query, [
                    deadline.title,
                    deadline.description or "",
                    project_name
                ])

                if score > 0:
                    results.append({
                        'type': 'deadline',
                        'id': str(deadline.id),
                        'title': deadline.title,
                        'subtitle': f"📅 {deadline.due_date.strftime('%d/%m/%Y')} • {project_name}",
                        'icon': ft.Icons.SCHEDULE_OUTLINED,
                        'score': score,
                        'data': deadline,
                        'category': 'Scadenze'
                    })

            # Sort by relevance score and limit results
            results.sort(key=lambda x: x['score'], reverse=True)
            return results[:max_results]

        except Exception as e:
            logger.error(f"Search error: {e}")
            return []

    def _calculate_relevance_score(self, query: str, fields: List[str]) -> float:
        """Calculate relevance score for search matching"""
        score = 0.0
        query_words = query.lower().split()

        for field in fields:
            if not field:
                continue

            field_lower = field.lower()

            # Exact match gets highest score
            if query in field_lower:
                score += 10.0

            # Word matches
            for word in query_words:
                if word in field_lower:
                    # Bonus for word at start
                    if field_lower.startswith(word):
                        score += 5.0
                    else:
                        score += 2.0

            # Fuzzy matching for typos (simple implementation)
            if len(query) > 3:
                for word in query_words:
                    if len(word) > 3:
                        for field_word in field_lower.split():
                            if self._fuzzy_match(word, field_word):
                                score += 1.0

        return score

    def _fuzzy_match(self, word1: str, word2: str, threshold: float = 0.8) -> bool:
        """Simple fuzzy matching for typo tolerance"""
        if len(word1) < 3 or len(word2) < 3:
            return False

        # Simple character overlap ratio
        common_chars = set(word1) & set(word2)
        total_chars = set(word1) | set(word2)

        if not total_chars:
            return False

        similarity = len(common_chars) / len(total_chars)
        return similarity >= threshold

class ModernSearchBox:
    """Modern search box with card-based results - Flet 28.x+ compatible"""

    def __init__(self, app_instance=None, on_result_click=None):
        self.app = app_instance
        self.on_result_click = on_result_click
        self.search_results = []
        self.is_searching = False
        self.search_timer = None

        # Initialize search engine
        try:
            self.search_engine = ProfessionalSearchEngine(app_instance) if app_instance else None
        except Exception as e:
            logger.error(f"Search engine initialization failed: {e}")
            self.search_engine = None
        
        self.results_overlay = None

        self._init_components()

    def _init_components(self):
        """Initialize modern search components"""
        
        # Modern search input with clear styling
        self.search_input = ft.TextField(
            hint_text="🔍 Cerca clienti, progetti, scadenze... (Ctrl+K)",
            border_radius=16,
            height=48,
            text_size=15,
            content_padding=ft.padding.symmetric(horizontal=20, vertical=14),
            border_color=ft.Colors.BLUE_200,
            focused_border_color=ft.Colors.BLUE_500,
            filled=True,
            fill_color=ft.Colors.BLUE_50,
            on_change=self._on_search_change,
            on_submit=self._on_search_submit,
            cursor_color=ft.Colors.BLUE_600,
            suffix=ft.Row([
                ft.IconButton(
                    icon=ft.Icons.KEYBOARD,
                    icon_size=16,
                    tooltip="Press F1 for keyboard shortcuts",
                    on_click=lambda e: self._show_shortcuts_hint() if hasattr(self, '_show_shortcuts_hint') else None,
                    icon_color=ft.Colors.GREY_600
                ),
                ft.IconButton(
                    icon=ft.Icons.CLEAR,
                    icon_size=16,
                    tooltip="Clear search",
                    on_click=self._clear_search,
                    visible=False,
                    icon_color=ft.Colors.GREY_600
                )
            ], tight=True, spacing=0)
        )

        # Results container that will be placed in the overlay
        self.results_view = ft.Container(
            content=ft.Card(
                content=ft.Column(
                    controls=[],
                    spacing=4,
                    scroll=ft.ScrollMode.AUTO,
                    height=320,
                ),
                elevation=8,
                shadow_color=ft.Colors.BLACK54,
            ),
            width=450,
            visible=False,
        )

    def _on_search_change(self, e):
        """Handle search input change"""
        query = e.control.value.strip()
        
        # Show/hide clear button
        clear_button = self.search_input.suffix.controls[1]
        clear_button.visible = len(query) > 0
        self.search_input.update()
        
        if len(query) >= 2:
            # Cancel previous search timer
            if self.search_timer:
                try:
                    self.search_timer.cancel()
                except:
                    pass
            
            # Start new search timer (300ms delay)
            import threading
            self.search_timer = threading.Timer(0.3, lambda: self._perform_search(query))
            self.search_timer.start()
        else:
            self._hide_results()

    def _on_search_submit(self, e):
        """Handle search submit (Enter key)"""
        query = e.control.value.strip()
        if query:
            self._perform_search(query)

    def _clear_search(self, e):
        """Clear search input and results"""
        self.search_input.value = ""
        clear_button = self.search_input.suffix.controls[1]
        clear_button.visible = False
        self.search_input.update()
        self._hide_results()
        
        # Focus back on search input
        self.search_input.focus()
        if hasattr(self, 'app') and hasattr(self.app, 'page'):
            self.app.page.update()

    def _manual_search(self, e):
        """Handle manual search button click"""
        query = self.search_input.value.strip()
        if query:
            self._perform_search(query)

    def _perform_search(self, query: str):
        """Perform search and show results"""
        try:
            logger.info(f"Performing search for: '{query}'")
            self._show_loading()

            # Get search results
            if self.search_engine:
                results = self.search_engine.search(query, max_results=8)
            else:
                results = self._simple_search(query)

            self.search_results = results
            self._show_results(results)

        except Exception as e:
            logger.error(f"Search error: {e}")
            self._show_error()

    def _simple_search(self, query: str):
        """Simple fallback search"""
        results = []
        if not self.app or not hasattr(self.app, 'db'):
            return results

        try:
            query_lower = query.lower()
            clients = self.app.db.get_all_clients()

            for client in clients[:5]:
                if query_lower in client.name.lower():
                    results.append({
                        'type': 'client',
                        'id': str(client.id),
                        'title': client.name,
                        'subtitle': client.business_name or client.email or "Cliente",
                        'icon': ft.Icons.PERSON_ROUNDED,
                        'data': client
                    })
        except Exception as e:
            logger.error(f"Simple search error: {e}")

        return results

    def _show_loading(self):
        """Show loading state"""
        loading_card = ft.Card(
            content=ft.Container(
                content=ft.Row([
                    ft.ProgressRing(width=20, height=20, stroke_width=3),
                    ft.Text("Ricerca in corso...", size=14, color=ft.Colors.GREY_600)
                ], spacing=12, alignment=ft.MainAxisAlignment.CENTER),
                padding=ft.padding.all(20)
            ),
            elevation=4,
            color=ft.Colors.WHITE
        )

        self._update_results_view([loading_card])

    def _show_results(self, results):
        """Show search results as modern cards"""
        if not results:
            self._show_no_results()
            return

        result_cards = []

        # Group results by category
        categories = {}
        for result in results:
            category = result.get('category', 'Altri')
            if category not in categories:
                categories[category] = []
            categories[category].append(result)

        # Create cards for each category
        for category, items in categories.items():
            # Category header
            if len(categories) > 1:
                category_header = ft.Container(
                    content=ft.Text(
                        category,
                        size=12,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.BLUE_700
                    ),
                    padding=ft.padding.only(left=16, top=8, bottom=4)
                )
                result_cards.append(category_header)

            # Result items
            for item in items:
                card = self._create_result_card(item)
                result_cards.append(card)

        self._update_results_view(result_cards)

    def _create_result_card(self, result):
        """Create a modern result card"""
        return ft.Card(
            content=ft.Container(
                content=ft.Row([
                    # Icon container
                    ft.Container(
                        content=ft.Icon(
                            result['icon'],
                            size=24,
                            color=ft.Colors.BLUE_600
                        ),
                        width=48,
                        height=48,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=12,
                        alignment=ft.alignment.center
                    ),
                    # Content
                    ft.Column([
                        ft.Text(
                            result['title'],
                            size=15,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_900,
                            max_lines=1,
                            overflow=ft.TextOverflow.ELLIPSIS
                        ),
                        ft.Text(
                            result['subtitle'],
                            size=13,
                            color=ft.Colors.GREY_600,
                            max_lines=1,
                            overflow=ft.TextOverflow.ELLIPSIS
                        )
                    ], spacing=4, expand=True, alignment=ft.MainAxisAlignment.CENTER),
                    # Arrow icon
                    ft.Icon(
                        ft.Icons.ARROW_FORWARD_IOS_ROUNDED,
                        size=16,
                        color=ft.Colors.GREY_400
                    )
                ], spacing=16, alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                padding=ft.padding.all(16),
                on_click=lambda _, r=result: self._handle_result_click(r)
            ),
            elevation=2,
            color=ft.Colors.WHITE,
            margin=ft.margin.symmetric(vertical=2)
        )

    def _show_no_results(self):
        """Show no results state"""
        no_results_card = ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.SEARCH_OFF_ROUNDED, size=32, color=ft.Colors.GREY_400),
                    ft.Text("Nessun risultato trovato", size=14, color=ft.Colors.GREY_600),
                    ft.Text("Prova con termini diversi", size=12, color=ft.Colors.GREY_500)
                ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(24),
                alignment=ft.alignment.center
            ),
            elevation=2,
            color=ft.Colors.WHITE
        )

        self._update_results_view([no_results_card])

    def _show_error(self):
        """Show error state"""
        error_card = ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.ERROR_OUTLINE_ROUNDED, size=32, color=ft.Colors.RED_400),
                    ft.Text("Errore durante la ricerca", size=14, color=ft.Colors.GREY_600),
                    ft.Text("Riprova più tardi", size=12, color=ft.Colors.GREY_500)
                ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(24),
                alignment=ft.alignment.center
            ),
            elevation=2,
            color=ft.Colors.WHITE
        )

        self._update_results_view([error_card])

    def _show_demo_results(self):
        """Show demo results for testing"""
        demo_results = [
            {
                'type': 'client',
                'id': '1',
                'title': 'Mario Rossi',
                'subtitle': '<EMAIL>',
                'icon': ft.Icons.PERSON_ROUNDED,
                'category': 'Clienti'
            },
            {
                'type': 'project',
                'id': '1',
                'title': 'Progetto Demo',
                'subtitle': 'PRJ-001 • Mario Rossi',
                'icon': ft.Icons.FOLDER_ROUNDED,
                'category': 'Progetti'
            },
            {
                'type': 'deadline',
                'id': '1',
                'title': 'Scadenza Test',
                'subtitle': '📅 31/12/2024 • Progetto Demo',
                'icon': ft.Icons.SCHEDULE_ROUNDED,
                'category': 'Scadenze'
            }
        ]

        self._show_results(demo_results)

    def _hide_results(self):
        """Hide search results"""
        self.results_view.visible = False
        self.results_view.opacity = 0
        
        # Remove from page overlay
        if self.app and hasattr(self.app, 'page') and self.results_view in self.app.page.overlay:
            try:
                self.app.page.overlay.remove(self.results_view)
            except ValueError:
                pass  # Already removed
        
        self._update_page()

    def _handle_result_click(self, result):
        """Handle result click"""
        self._hide_results()
        self.search_input.value = ""

        if self.on_result_click:
            self.on_result_click(result)

    def _update_page(self):
        """Update the page"""
        if self.app and hasattr(self.app, 'page'):
            self.app.page.update()

    def get_input_control(self):
        """Returns the search input TextField"""
        return self.search_input

    def get_results_view(self):
        """Returns the results view Container for the overlay"""
        return self.results_view

    def _update_results_view(self, controls: List[ft.Control]):
        """Updates and shows the results view with calculated positioning."""
        # Calculate position based on page center and header height
        if self.app and hasattr(self.app, 'page'):
            page_width = getattr(self.app.page, 'width', 1200)
            
            # Position results in center, below header (~80px)
            self.results_view.top = 85  # Just below header
            self.results_view.left = (page_width / 2) - 225  # Center the 450px width
            
            # Update content and make visible
            results_column = self.results_view.content.content
            results_column.controls = controls
            self.results_view.visible = True
            self.results_view.opacity = 1
            
            # Add to page overlay if not already there
            if self.results_view not in self.app.page.overlay:
                self.app.page.overlay.append(self.results_view)
        else:
            logger.warning("App page not available, cannot show results.")
        
        self._update_page()

class Header:
    """Professional header component with modern search"""

    def __init__(
        self,
        on_search: Callable[[str], None],
        on_notification_click: Callable[[], None],
        on_settings_click: Callable[[], None],
        app_instance=None
    ):
        self.on_search = on_search
        self.on_notification_click = on_notification_click
        self.on_settings_click = on_settings_click
        self.app = app_instance

        self.title = "Dashboard"
        self.notification_count = 0
        self.show_back_button = False
        self.show_settings = True  # Show settings button by default

        # Initialize modern search box
        self.search_box = ModernSearchBox(
            app_instance=app_instance,
            on_result_click=self._handle_search_result_click
        )

        self._init_components()

    def _init_components(self):
        """Initialize header components"""
        pass  # Components are now handled by ModernSearchBox

    def _handle_search_result_click(self, result):
        """Handle search result clicks"""
        try:
            if not hasattr(self.app, 'main_layout'):
                logger.error("Main layout not available for navigation")
                return

            result_type = result['type']
            result_id = result['id']

            logger.info(f"Navigating to {result_type}: {result_id}")

            if result_type == 'client':
                # First navigate to clients view, then show client detail
                self.app.main_layout._navigate_to("clients")
                self.app.main_layout.navigate_to_detail("client_detail", result_id)
            elif result_type == 'project':
                # First navigate to projects view, then show project detail
                self.app.main_layout._navigate_to("projects")
                self.app.main_layout.navigate_to_detail("project_detail", result_id)
            elif result_type == 'deadline':
                # For deadlines, just navigate to deadlines view
                self.app.main_layout._navigate_to("deadlines")
            else:
                logger.warning(f"Unknown result type: {result_type}")

        except Exception as e:
            logger.error(f"Navigation error: {e}")
            if hasattr(self.app, 'page'):
                self.app.page.open(
                    ft.SnackBar(
                        content=ft.Text(f"Errore durante la navigazione: {str(e)}"),
                        duration=3000
                    )
                )

    def _create_title_section(self) -> ft.Container:
        """Crea la sezione titolo"""
        title_controls = []
        
        # Add back button if needed
        if self.show_back_button:
            back_button = ft.IconButton(
                icon=ft.Icons.ARROW_BACK,
                icon_color=ft.Colors.BLUE_600,
                icon_size=24,
                tooltip="Torna indietro",
                on_click=lambda _: self._handle_back_click()
            )
            title_controls.append(back_button)
        
        # Logo and title section
        logo_title_row = ft.Row([
            ft.Image(
                src="assets/logo_v2.png",
                width=32,
                height=32,
                fit=ft.ImageFit.CONTAIN
            ),
            ft.Column([
                ft.Text(
                    self.title,
                    size=24,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                ft.Text(
                    datetime.now().strftime("%A, %d %B %Y"),
                    size=12,
                    color=ft.Colors.GREY_500
                )
            ], spacing=2)
        ], spacing=12, vertical_alignment=ft.CrossAxisAlignment.CENTER)
        
        title_controls.append(logo_title_row)
        
        return ft.Container(
            content=ft.Row(
                controls=title_controls,
                spacing=10,
                vertical_alignment=ft.CrossAxisAlignment.CENTER
            ),
            padding=ft.padding.only(left=20)
        )
    
    def _create_actions_section(self) -> ft.Container:
        """Create actions section with icons"""
        actions = []
        
        # Back button (se visibile)
        if self.show_back_button:
            actions.append(
                ft.IconButton(
                    icon=ft.Icons.ARROW_BACK,
                    tooltip="Indietro",
                    on_click=lambda e: self._handle_back_click(),
                    icon_color=ft.Colors.BLUE_600
                )
            )
        
        # Keyboard shortcuts button
        actions.append(
            ft.IconButton(
                icon=ft.Icons.KEYBOARD,
                tooltip="Keyboard Shortcuts (F1)",
                icon_color=ft.Colors.GREY_600,
                on_click=self._show_shortcuts_help,
                bgcolor=ft.Colors.TRANSPARENT
            )
        )
        
        # Notification button
        actions.append(
            ft.Container(
                content=ft.Row([
                    ft.IconButton(
                        icon=ft.Icons.NOTIFICATIONS_NONE,
                        tooltip="Notifiche",
                        on_click=lambda e: self.on_notification_click() if self.on_notification_click else None,
                        icon_color=ft.Colors.BLUE_600
                    ),
                    # Simple badge using container
                    ft.Container(
                        content=ft.Text(
                            str(self.notification_count),
                            size=10,
                            color=ft.Colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        bgcolor=ft.Colors.RED,
                        border_radius=8,
                        padding=ft.padding.all(4),
                        width=16,
                        height=16,
                        alignment=ft.alignment.center,
                        visible=self.notification_count > 0,
                        margin=ft.margin.only(left=-20, top=-5)  # Overlap effect
                    )
                ], spacing=0, alignment=ft.MainAxisAlignment.START),
                width=50  # Fixed width to accommodate badge
            )
        )

        # Update button - Quick access to manual updates
        actions.append(
            ft.IconButton(
                icon=ft.Icons.SYSTEM_UPDATE,
                tooltip="Controlla Aggiornamenti (Ctrl+U)",
                on_click=lambda e: self._handle_manual_update(),
                icon_color=ft.Colors.BLUE_600,
                bgcolor=ft.Colors.BLUE_50
            )
        )

        # Settings button
        if self.show_settings:
            actions.append(
                ft.IconButton(
                    icon=ft.Icons.SETTINGS,
                    tooltip="Impostazioni",
                    on_click=lambda e: self.on_settings_click() if self.on_settings_click else None,
                    icon_color=ft.Colors.GREY_600
                )
            )

        # Statistics button with dropdown menu
        def show_stats_menu(e):
            # Create popup menu for statistics options
            def send_full_report(e):
                self._handle_send_statistics('full')
                
            def send_deadlines_report(e):
                self._handle_send_statistics('deadlines')
                
            def close_menu(e):
                menu.open = False
                self.app.page.update()

            menu = ft.AlertDialog(
                title=ft.Text("Invia Statistiche", weight=ft.FontWeight.BOLD),
                content=ft.Column([
                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.ANALYTICS, color=ft.Colors.BLUE_600),
                        title=ft.Text("Report Completo"),
                        subtitle=ft.Text("Tutte le statistiche dell'applicazione"),
                        on_click=send_full_report
                    ),
                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.SCHEDULE, color=ft.Colors.ORANGE_600),
                        title=ft.Text("Report Scadenze"),
                        subtitle=ft.Text("Solo le scadenze in arrivo"),
                        on_click=send_deadlines_report
                    ),
                ], tight=True),
                actions=[
                    ft.TextButton("Annulla", on_click=close_menu)
                ]
            )

            self.app.page.overlay.append(menu)
            menu.open = True
            self.app.page.update()

        actions.append(
            ft.IconButton(
                icon=ft.Icons.ANALYTICS,
                tooltip="Invia Statistiche",
                on_click=show_stats_menu,
                icon_color=ft.Colors.GREEN_600
            )
        )

        return ft.Container(
            content=ft.Row(
                actions,
                spacing=8,
                alignment=ft.MainAxisAlignment.END
            )
        )
    
    def _create_quick_actions(self) -> ft.Container:
        """Crea i pulsanti di azione rapida"""
        
        quick_actions = [
            {
                "icon": ft.Icons.ADD_CIRCLE_OUTLINE,
                "text": "Nuovo Cliente",
                "color": ft.Colors.BLUE_600,
                "action": "new_client"
            },
            {
                "icon": ft.Icons.WORK_OUTLINE,
                "text": "Nuovo Progetto",
                "color": ft.Colors.GREEN_600,
                "action": "new_project"
            },
            {
                "icon": ft.Icons.SCHEDULE,
                "text": "Nuova Scadenza",
                "color": ft.Colors.ORANGE_600,
                "action": "new_deadline"
            }
        ]
        
        action_buttons = []
        for action in quick_actions:
            button = ft.Container(
                content=ft.Row([
                    ft.Icon(
                        action["icon"],
                        color=action["color"],
                        size=16
                    ),
                    ft.Text(
                        action["text"],
                        size=12,
                        color=action["color"],
                        weight=ft.FontWeight.BOLD
                    )
                ], spacing=6),
                padding=ft.padding.symmetric(horizontal=12, vertical=6),
                border_radius=20,
                border=ft.border.all(1, action["color"]),
                bgcolor=ft.Colors.WHITE,
                on_click=lambda _, a=action["action"]: self._handle_quick_action(a),
                tooltip=f"Crea {action['text'].lower()}"
            )
            action_buttons.append(button)
        
        return ft.Container(
            content=ft.Row(
                controls=action_buttons,
                spacing=12
            ),
            padding=ft.padding.symmetric(horizontal=20, vertical=8)
        )
    
    def _handle_quick_action(self, action: str):
        """Gestisce le azioni rapide"""
        print(f"Azione rapida: {action}")
        
        if action == "new_client":
            # Naviga alla vista clienti e mostra il form
            clients_view = self.app.main_layout.get_view("clients")
            if clients_view:
                clients_view._show_client_form()
                self.app.main_layout._navigate_to("clients")
        
        elif action == "new_project":
            # Naviga alla vista progetti e mostra il form
            projects_view = self.app.main_layout.get_view("projects")
            if projects_view:
                projects_view._show_project_form()
                self.app.main_layout._navigate_to("projects")
        
        elif action == "new_deadline":
            # Naviga alla vista scadenze e mostra il form
            deadlines_view = self.app.main_layout.get_view("deadlines")
            if deadlines_view:
                deadlines_view._show_deadline_form()
                self.app.main_layout._navigate_to("deadlines")
    
    def build(self) -> ft.Container:
        """Build modern header with new search component"""
        # Main header content
        main_content = ft.Column([
            # Main row
            ft.Container(
                content=ft.Row([
                    # Title section
                    self._create_title_section(),

                    # Spacer
                    ft.Container(expand=True),

                    # Modern search box
                    self.search_box.get_input_control(),

                    # Spacer
                    ft.Container(expand=True),

                    # Actions section
                    self._create_actions_section()
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                height=80,
                padding=ft.padding.symmetric(vertical=16)
            ),

            # Quick actions (optional)
            # self._create_quick_actions() if self.title == "Dashboard" else ft.Container(),

            # Divider
            ft.Divider(color=ft.Colors.GREY_200, height=1)
        ], spacing=0)

        return ft.Container(
            content=main_content,
            bgcolor=ft.Colors.WHITE,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.15, ft.Colors.BLACK),
                offset=ft.Offset(0, 4)
            ),
            # Allow search overlay to extend beyond header bounds
            clip_behavior=ft.ClipBehavior.NONE
        )
    
    def _handle_back_click(self):
        """Gestisce il click del pulsante indietro"""
        if self.app and hasattr(self.app, 'main_layout'):
            self.app.main_layout.go_back()

    def _handle_manual_update(self):
        """Handle manual update trigger from header"""
        try:
            if hasattr(self.app, 'update_manager') and self.app.update_manager:
                # Show loading notification
                if hasattr(self.app, 'page'):
                    self.app.page.open(
                        ft.SnackBar(
                            content=ft.Text("🔄 Controllo aggiornamenti..."),
                            duration=2000,
                            bgcolor=ft.Colors.BLUE_600
                        )
                    )
                
                # Trigger manual update check
                self.app.update_manager.check_for_updates_manual()
            else:
                # Fallback: navigate to settings updates section
                if hasattr(self.app, 'main_layout') and self.app.main_layout:
                    self.app.main_layout._navigate_to("settings")
                    # Note: You could enhance this to automatically select the updates section
                    
        except Exception as e:
            logger.error(f"Manual update error: {e}")
            if hasattr(self.app, 'page'):
                self.app.page.open(
                    ft.SnackBar(
                        content=ft.Text(f"❌ Errore controllo aggiornamenti: {str(e)}"),
                        duration=3000,
                        bgcolor=ft.Colors.RED_600
                    )
                )

    def set_title(self, title: str):
        """Imposta il titolo dell'header"""
        self.title = title

    def set_back_button_visible(self, visible: bool):
        """Imposta la visibilità del pulsante indietro"""
        self.show_back_button = visible
    
    def update_notification_count(self, count: int):
        """Aggiorna il contatore delle notifiche"""
        self.notification_count = count
    
    def clear_search(self):
        """Pulisce il campo di ricerca"""
        if self.search_box and self.search_box.search_input:
            self.search_box.search_input.value = ""
            self.search_box._hide_results()

    def get_search_query(self) -> str:
        """Restituisce la query di ricerca corrente"""
        if self.search_box and self.search_box.search_input:
            return self.search_box.search_input.value
        return ""
    
    def _handle_send_statistics(self, report_type: str):
        """Gestisce l'invio delle statistiche"""
        try:
            import json
            import os
            from core.services.statistics_service import StatisticsService
            from core.config import AppConfig

            # Load email settings from JSON file instead of AppConfig
            settings_file = os.path.join("data", "settings.json")
            if not os.path.exists(settings_file):
                self._show_error_dialog("File impostazioni non trovato. Configura SMTP nelle impostazioni.")
                return

            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

            email_settings = settings.get('email', {})

            # Verifica configurazione email
            if not (email_settings.get('server') and email_settings.get('username') and email_settings.get('password')):
                self._show_error_dialog("Configurazione email non valida. Configura SMTP nelle impostazioni.")
                return

            # Ottieni destinatari dalle impostazioni
            recipients = []

            # Aggiungi il mittente configurato
            if email_settings.get('username'):
                recipients.append(email_settings['username'])

            # Aggiungi destinatari aggiuntivi dalle impostazioni reports e notifications
            reports_recipients = settings.get('reports', {}).get('recipients', [])
            if reports_recipients:
                recipients.extend(reports_recipients)

            notification_recipients = settings.get('notifications', {}).get('reminder_recipients', [])
            if notification_recipients:
                recipients.extend(notification_recipients)

            # Remove duplicates
            recipients = list(set(recipients))

            if not recipients:
                self._show_error_dialog("Nessun destinatario configurato. Configura gli indirizzi email nelle impostazioni.")
                return

            # Crea servizio statistiche con AppConfig
            config = AppConfig()
            stats_service = StatisticsService(self.app.db_manager, config)

            # Update email service configuration with JSON settings
            stats_service.email_service.smtp_config.update({
                'smtp_server': email_settings.get('server', ''),
                'smtp_port': email_settings.get('port', 587),
                'smtp_username': email_settings.get('username', ''),
                'smtp_password': email_settings.get('password', ''),
                'smtp_use_tls': email_settings.get('use_tls', True),
                'from_name': email_settings.get('sender_name', 'Agevolami PM'),
                'from_email': email_settings.get('sender_email', ''),
                'enabled': bool(email_settings.get('server'))
            })

            # Test connection first
            if not stats_service.email_service.test_connection():
                self._show_error_dialog("Configurazione email non valida. Verifica le impostazioni SMTP.")
                return

            # Mostra dialog di conferma
            self._show_send_confirmation_dialog(stats_service, recipients, report_type)

        except Exception as e:
            self._show_error_dialog(f"Errore durante l'invio delle statistiche: {str(e)}")
    
    def _show_send_confirmation_dialog(self, stats_service, recipients: list, report_type: str):
        """Mostra dialog di conferma per l'invio"""
        report_name = "Report Completo" if report_type == 'full' else "Report Scadenze"
        recipients_text = ", ".join(recipients)
        
        def send_report(e):
            dialog.open = False
            self.app.page.update()
            
            # Mostra indicatore di caricamento
            loading_dialog = ft.AlertDialog(
                title=ft.Text("Invio in corso..."),
                content=ft.Row([
                    ft.ProgressRing(width=16, height=16),
                    ft.Text("Generazione e invio del report...")
                ], spacing=10),
                modal=True
            )
            
            self.app.page.overlay.append(loading_dialog)
            loading_dialog.open = True
            self.app.page.update()
            
            # Invia report
            try:
                success = stats_service.send_statistics_email(recipients, report_type)
                
                loading_dialog.open = False
                self.app.page.update()
                
                if success:
                    self._show_success_dialog(f"{report_name} inviato con successo!")
                else:
                    self._show_error_dialog(f"Errore durante l'invio del {report_name.lower()}")
                    
            except Exception as ex:
                loading_dialog.open = False
                self.app.page.update()
                self._show_error_dialog(f"Errore: {str(ex)}")
        
        def cancel_send(e):
            dialog.open = False
            self.app.page.update()
        
        dialog = ft.AlertDialog(
            title=ft.Text(f"Conferma Invio {report_name}"),
            content=ft.Column([
                ft.Text(f"Vuoi inviare il {report_name.lower()} ai seguenti destinatari?"),
                ft.Text(recipients_text, weight=ft.FontWeight.BOLD),
                ft.Text("\nIl report verrà generato e inviato via email.", size=12, color=ft.Colors.GREY_600)
            ], tight=True, spacing=10),
            actions=[
                ft.TextButton("Annulla", on_click=cancel_send),
                ft.ElevatedButton("Invia", on_click=send_report)
            ],
            modal=True
        )
        
        self.app.page.overlay.append(dialog)
        dialog.open = True
        self.app.page.update()
    
    def _show_success_dialog(self, message: str):
        """Mostra dialog di successo"""
        def close_dialog(e):
            dialog.open = False
            self.app.page.update()
        
        dialog = ft.AlertDialog(
            title=ft.Text("Successo", color=ft.Colors.GREEN),
            content=ft.Text(message),
            actions=[ft.TextButton("OK", on_click=close_dialog)],
            modal=True
        )
        
        self.app.page.overlay.append(dialog)
        dialog.open = True
        self.app.page.update()
    
    def _show_error_dialog(self, message: str):
        """Mostra dialog di errore"""
        def close_dialog(e):
            dialog.open = False
            self.app.page.update()
        
        dialog = ft.AlertDialog(
            title=ft.Text("Errore", color=ft.Colors.RED),
            content=ft.Text(message),
            actions=[ft.TextButton("OK", on_click=close_dialog)],
            modal=True
        )
        
        self.app.page.overlay.append(dialog)
        dialog.open = True
        self.app.page.update()

    def _show_shortcuts_help(self, e):
        """Show keyboard shortcuts help"""
        if self.app and hasattr(self.app, 'main_layout'):
            self.app.main_layout._show_shortcuts_help()