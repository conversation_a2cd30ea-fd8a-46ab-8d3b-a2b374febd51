#!/usr/bin/env python3
"""
Test script to verify update checking functionality
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.append('src')

def test_update_check():
    """Test the update checking functionality"""
    print("🔍 Testing Update Check Functionality")
    print("=" * 50)
    
    try:
        from core.updater import AutoUpdater
        
        # Test with current version
        print("Testing with current version (2.0.10)...")
        updater_current = AutoUpdater(current_version="2.0.10")
        
        # Force check for updates
        print("Checking for updates...")
        update_info = updater_current.check_for_updates(force_check=True)
        
        if update_info is None:
            print("❌ Failed to check for updates (network/API issue)")
            return False
        elif update_info.get('available', False):
            print(f"✅ Update available!")
            print(f"   Current version: {update_info.get('current_version', 'Unknown')}")
            print(f"   Latest version: {update_info.get('latest_version', 'Unknown')}")
            print(f"   Download URL: {update_info.get('download_url', 'Not available')}")
            print(f"   Release date: {update_info.get('release_date', 'Unknown')}")
        else:
            print("✅ No updates available - you have the latest version")
        
        # Test with older version to see if update detection works
        print("\nTesting with older version (1.0.0)...")
        updater_old = AutoUpdater(current_version="1.0.0")
        update_info_old = updater_old.check_for_updates(force_check=True)
        
        if update_info_old is None:
            print("❌ Failed to check for updates with old version")
        elif update_info_old.get('available', False):
            print(f"✅ Update correctly detected for old version!")
            print(f"   Would update from: {update_info_old.get('current_version', 'Unknown')}")
            print(f"   To version: {update_info_old.get('latest_version', 'Unknown')}")
        else:
            print("⚠️  No update detected for old version (unexpected)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing update check: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_github_api_access():
    """Test GitHub API access"""
    print("\n🔍 Testing GitHub API Access")
    print("=" * 50)
    
    try:
        from core.updater import AutoUpdater
        
        updater = AutoUpdater()
        
        # Test authentication
        print("Testing GitHub API authentication...")
        auth_result = updater.test_authentication()
        print(f"Authentication result: {auth_result}")
        
        # Test getting latest release
        print("Testing latest release fetch...")
        latest_release = updater._get_latest_release()
        
        if latest_release:
            tag_name = latest_release.get('tag_name', 'Unknown')
            published_at = latest_release.get('published_at', 'Unknown')
            assets_count = len(latest_release.get('assets', []))
            
            print(f"✅ Latest release: {tag_name}")
            print(f"   Published: {published_at}")
            print(f"   Assets: {assets_count}")
            
            # Check for Windows asset
            download_url = updater._get_download_url(latest_release)
            if download_url:
                print(f"✅ Windows download URL found")
            else:
                print("⚠️  No Windows download URL found")
        else:
            print("❌ Failed to fetch latest release")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing GitHub API: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_version_comparison():
    """Test version comparison logic"""
    print("\n🔍 Testing Version Comparison")
    print("=" * 50)
    
    try:
        from core.updater import AutoUpdater
        
        updater = AutoUpdater()
        
        # Test cases
        test_cases = [
            ("1.0.0", "1.0.1", True),   # Should update
            ("1.0.1", "1.0.0", False),  # Should not update
            ("1.0.0", "2.0.0", True),   # Should update (major)
            ("2.0.0", "1.9.9", False),  # Should not update
            ("1.0.0", "1.0.0", False),  # Same version
        ]
        
        all_passed = True
        for current, latest, expected in test_cases:
            result = updater._is_newer_version(latest, current)
            status = "✅" if result == expected else "❌"
            print(f"{status} {current} -> {latest}: {result} (expected: {expected})")
            if result != expected:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing version comparison: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Agevolami PM - Update Check Test Suite")
    print("=" * 60)
    
    results = []
    
    # Run tests
    results.append(("Version Comparison", test_version_comparison()))
    results.append(("GitHub API Access", test_github_api_access()))
    results.append(("Update Check", test_update_check()))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Update checking is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
