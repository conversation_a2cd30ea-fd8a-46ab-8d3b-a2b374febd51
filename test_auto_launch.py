#!/usr/bin/env python3
"""
Test script to verify auto-launch on startup functionality
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.append('src')

def test_startup_functionality():
    """Test the Windows startup functionality"""
    print("🔍 Testing Auto-Launch on Startup Functionality")
    print("=" * 50)
    
    try:
        from core.utils.windows_utils import WindowsStartupManager
        
        # Test 1: Check if we're on Windows
        is_windows = WindowsStartupManager.is_windows()
        print(f"✅ Running on Windows: {is_windows}")
        
        if not is_windows:
            print("❌ Auto-launch only works on Windows")
            return False
        
        # Test 2: Check current startup status
        startup_enabled = WindowsStartupManager.is_startup_enabled()
        print(f"✅ Auto-launch currently enabled: {startup_enabled}")
        
        # Test 3: Get executable path
        exe_path = WindowsStartupManager.get_executable_path()
        print(f"✅ Executable path: {exe_path}")
        
        # Test 4: Check registry entry (if enabled)
        if startup_enabled:
            print("✅ Registry entry exists for auto-launch")
        else:
            print("ℹ️  No registry entry found (auto-launch disabled)")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing startup functionality: {e}")
        return False

def test_update_manager():
    """Test the update manager functionality"""
    print("\n🔍 Testing Update Manager Functionality")
    print("=" * 50)
    
    try:
        from core.update_manager import UpdateManager
        from core.updater import AutoUpdater
        
        # Test 1: Create update manager
        print("✅ UpdateManager imported successfully")
        
        # Test 2: Create updater
        updater = AutoUpdater(current_version="1.0.0")  # Use old version to test
        print(f"✅ AutoUpdater created - Repository: {updater.repo_owner}/{updater.repo_name}")
        
        # Test 3: Check if update check file exists
        update_check_file = Path("data/last_update_check.json")
        if update_check_file.exists():
            print(f"✅ Update check file exists: {update_check_file}")
            
            # Read last check time
            import json
            with open(update_check_file, 'r') as f:
                data = json.load(f)
            
            import time
            last_check = data.get('last_check', 0)
            time_since_check = time.time() - last_check
            hours_since_check = time_since_check / 3600
            
            print(f"✅ Last update check: {hours_since_check:.1f} hours ago")
        else:
            print("ℹ️  No previous update check found")
        
        # Test 4: Test GitHub API access (without making actual request)
        print("✅ Update manager components loaded successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing update manager: {e}")
        return False

def test_main_app_integration():
    """Test integration in main app"""
    print("\n🔍 Testing Main App Integration")
    print("=" * 50)
    
    try:
        # Check if main.py has the update manager initialization
        main_file = Path("src/main.py")
        if main_file.exists():
            with open(main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "UpdateManager" in content:
                print("✅ UpdateManager is integrated in main.py")
            else:
                print("❌ UpdateManager not found in main.py")
                return False
            
            if "check_for_updates_on_startup" in content:
                print("✅ Startup update check is configured")
            else:
                print("❌ Startup update check not configured")
                return False
        else:
            print("❌ main.py not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing main app integration: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Agevolami PM - Auto-Launch Test Suite")
    print("=" * 60)
    
    results = []
    
    # Run tests
    results.append(("Startup Functionality", test_startup_functionality()))
    results.append(("Update Manager", test_update_manager()))
    results.append(("Main App Integration", test_main_app_integration()))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Auto-launch functionality is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
